from itertools import combinations
import math
import pandas as pd
import time
import sys
from functools import lru_cache

# Sabitler
MAX_COALITION_SIZE = 8  # Koalisyon büyüklüğü sınırı
KapasiteTır = 20275
KapasiteKamyon = 11059
UgramaTır = 1500
UgramaKamyon = 1000

excel_path = r"C:\Users\<USER>\OneDrive - Horoz Lojistik\_slms_\Rota_oluşturma\DATA_PEYMAN.xlsx"

print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'
total_start_time = time.time()

def clear_output_sheet():
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        print(f"Output sekmesi oluşturuluyor: {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def precompute_compatibility(rules):
    """Plaka uyumluluk matrisini önceden hesaplar"""
    all_plakas = set(plaka for rule in rules for plaka in rule)
    compat_matrix = {}
    
    for plaka1 in all_plakas:
        for plaka2 in all_plakas:
            compat = any({plaka1, plaka2}.issubset(rule) for rule in rules)
            compat_matrix[(plaka1, plaka2)] = compat
            compat_matrix[(plaka2, plaka1)] = compat
    
    return compat_matrix

def is_valid_addition(new_plaka, current_set, compat_matrix):
    """Yeni plakanın mevcut kümeyle uyumlu olup olmadığını kontrol eder"""
    if not current_set:
        return True
        
    return all(compat_matrix.get((new_plaka, p), False) for p in current_set)

@lru_cache(maxsize=None)
def compute_coalition_min_cost_cached(coalition_tuple, allowed_rules_tuple):
    """Koalisyon maliyetini önbelleğe alarak hesaplar"""
    coalition = list(coalition_tuple)
    allowed_rules = [set(rule) for rule in allowed_rules_tuple]
    compat_matrix = precompute_compatibility(allowed_rules)
    return _compute_coalition_min_cost(coalition, compat_matrix)

def _compute_coalition_min_cost(coalition, compat_matrix):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır
    
    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment
        
        # Geçerli maliyet alt sınırı hesapla
        current_size = len(current_plaka_set)
        current_cost_lb = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost_lb >= best_cost:
            return
        
        if idx == n:
            final_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
            if final_cost < best_cost:
                best_cost = final_cost
                best_assignment = current_assignment.copy()
                chosen_K = K_type
            return
        
        customer_idx = coalition[idx]
        p = customers[customer_idx]
        options = ['z'] if p.get("is_full_truck", False) else ['x', 'y', 'z', 'w']
        
        for option in options:
            new_plaka = p[option]["plaka"]
            
            # Erken budama: Uyumsuz plaka kombinasyonları
            if not is_valid_addition(new_plaka, current_plaka_set, compat_matrix):
                continue
                
            new_plaka_set = current_plaka_set | {new_plaka}
            
            # 3'ten fazla plaka kontrolü
            if len(new_plaka_set) > 3:
                continue
                
            plaka_fiyat = p[option][K_type]
            new_max_K = max(current_max_K, plaka_fiyat)
            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]
    
    backtrack(0, {}, 0, 0, set())
    return best_cost if best_cost != math.inf else None, best_assignment, chosen_K

def valid_ldm(coalition):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def reconstruct(mask, partition):
    if mask == 0:
        return []
    prev_mask, sub = partition[mask]
    return reconstruct(prev_mask, partition) + [sub]

output_df = clear_output_sheet()

truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
truck_prices = {}
for _, row in truck_prices_df.iterrows():
    plaka = str(int(row['plaka'])).zfill(3)
    truck_prices[plaka] = {'kamyon': row['kamyon'], 'tır': row['tır']}

customers_df = pd.read_excel(excel_path, sheet_name="customers")
customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
grouped_customers = customers_df.groupby('tarih')

sonuclar = []
cache_hits = 0
cache_misses = 0

for tarih, group_df in grouped_customers:
    group_start_time = time.time()
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor...")

    customers = []
    for _, row in group_df.iterrows():
        ldm_value = row["ldm"]
        plaka_x = str(int(row["plaka x"])).zfill(3)
        plaka_y = str(int(row["plaka y"])).zfill(3)
        plaka_z = str(int(row["plaka z"])).zfill(3)
        plaka_w = str(int(row["plaka w"])).zfill(3)
        
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "x": {
                "plaka": plaka_x,
                "kamyon": truck_prices[plaka_x]['kamyon'],
                "tır": truck_prices[plaka_x]['tır'],
                "tldesi": row["tldesi x"]
            },
            "y": {
                "plaka": plaka_y,
                "kamyon": truck_prices[plaka_y]['kamyon'],
                "tır": truck_prices[plaka_y]['tır'],
                "tldesi": row["tldesi y"]
            },
            "z": {
                "plaka": plaka_z,
                "kamyon": truck_prices[plaka_z]['kamyon'],
                "tır": truck_prices[plaka_z]['tır'],
                "tldesi": row["tldesi z"]
            },
            "w": {
                "plaka": plaka_w,
                "kamyon": truck_prices[plaka_w]['kamyon'],
                "tır": truck_prices[plaka_w]['tır'],
                "tldesi": row["tldesi w"]
            },
            "is_full_truck": False
        }
        customers.append(customer)

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()
    allowed_rules_tuple = tuple(tuple(rule) for rule in allowed_rules)

    N = len(customers)
    print(f"Toplam müşteri sayısı: {N}")

    def get_valid_masks(N):
        valid_masks = []
        for mask in range(1, 1 << N):
            size = bin(mask).count("1")
            if size > MAX_COALITION_SIZE:
                continue
            if valid_ldm([i for i in range(N) if mask & (1 << i)]):
                valid_masks.append(mask)
        return valid_masks

    valid_masks = get_valid_masks(N)
    print(f"Geçerli koalisyon sayısı: {len(valid_masks)}/{2**N - 1}")

    valid_coalitions = {}
    cache = {}
    global cache_hits, cache_misses
    cache_hits = 0
    cache_misses = 0

    for mask in valid_masks:
        coalition = tuple(sorted(i for i in range(N) if mask & (1 << i)))
        
        # Önbellek kontrolü
        cache_key = (coalition, allowed_rules_tuple)
        if cache_key in cache:
            cache_hits += 1
            valid_coalitions[mask] = cache[cache_key]
            continue
            
        cache_misses += 1
        cost, assignment, K_used = compute_coalition_min_cost_cached(coalition, allowed_rules_tuple)
        
        if cost is not None:
            result = (cost, assignment, K_used)
            valid_coalitions[mask] = result
            cache[cache_key] = result

    print(f"Önbellek isabetleri: {cache_hits}, Önbellek ıskaları: {cache_misses}")

    dp = [math.inf] * (1 << N)
    partition = [None] * (1 << N)
    dp[0] = 0

    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining

    final_partition = reconstruct((1 << N) - 1, partition)

    for sub in final_partition:
        cost, assignment, K_used = valid_coalitions[sub]
        coalition_names = [customers[i]["alıcı"] for i in range(N) if sub & (1 << i)]
        coalition_country = [customers[i]["il"] for i in range(N) if sub & (1 << i)]
        coalition_ldm = [int(customers[i]["ldm"]) for i in range(N) if sub & (1 << i)]
        coalition_assignment = [assignment[i] for i in range(N) if sub & (1 << i)]

        assignment_details = {
            customers[i]["alıcı"]: {
                'seçim': assignment[i],
                'hacim': int(customers[i]["hacim"])
            } for i in assignment if sub & (1 << i)
        }
        
        toplam_hacim = int(sum(customers[i]["hacim"] for i in range(N) if sub & (1 << i)))
        
        if print_to_terminal:
            print(f"Grup: {coalition_names}, Grup Atamaları: {assignment_details}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim}, Grup Maliyeti: {cost}")
        
        sonuclar.append({
            'tarih': tarih,
            'grup': str(coalition_names),
            'secim': str(coalition_assignment),
            'il': str(coalition_country),
            'hacim': str(coalition_ldm),
            'atamalar': str(assignment_details),
            'arac': K_used,
            'toplam_hacim': toplam_hacim,
            'maliyet': cost
        })
    
    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Bu grup işlemi {group_duration:.2f} saniye sürdü")

total_duration = time.time() - total_start_time
if print_to_terminal:
    print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")
    print(f"Toplam önbellek isabetleri: {cache_hits}, ıskaları: {cache_misses}")

yeni_sonuclar_df = pd.DataFrame(sonuclar)
if not output_df.empty:
    output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
else:
    output_df = yeni_sonuclar_df

with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    output_df.to_excel(writer, sheet_name='output', index=False)

print("\nSonuçlar başarıyla Excel'e kaydedildi.")