import pandas as pd
import ast

def create_output_summary(excel_path):
    # Output sekmesini oku
    df = pd.read_excel(excel_path, sheet_name='output')
    
    # Sonuçları saklayacak liste
    summary_data = []
    
    # Her satır için işlem yap
    for _, row in df.iterrows():
        tarih = row['tarih']
        
        # String olarak kaydedilen atamalar sözlüğünü dict'e çevir
        atamalar = ast.literal_eval(row['atamalar'])
        
        # Her müşteri için seçimleri analiz et
        for musteri, detay in atamalar.items():
            # Detayları dict'e çevir
            detay_dict = ast.literal_eval(str(detay))
            secim = detay_dict['seçim']
            hacim = detay_dict['hacim']
            
            # Mevcut müşterinin önceki kaydını bul
            existing = next((item for item in summary_data if item['tarih'] == tarih and item['musteri'] == musteri), None)
            
            if existing:
                # Mevcut kayıt varsa hacmi ekle
                existing[secim] += hacim
            else:
                # Yeni kayıt oluştur
                new_record = {
                    'tarih': tarih,
                    'musteri': musteri,
                    's1': hacim if secim == 's1' else 0,
                    's2': hacim if secim == 's2' else 0,
                    's3': hacim if secim == 's3' else 0,
                    's4': hacim if secim == 's4' else 0,
                    's5': hacim if secim == 's5' else 0,
                    's6': hacim if secim == 's6' else 0
                }
                summary_data.append(new_record)
    
    # DataFrame oluştur
    summary_df = pd.DataFrame(summary_data)
    
    # Tarihe göre sırala
    summary_df = summary_df.sort_values('tarih')
    
    # Excel'e kaydet (openpyxl motorunu kullanarak)
    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        summary_df.to_excel(writer, sheet_name='output_summary', index=False)
    
    print("Özet tablo 'output_summary' sekmesine kaydedildi.")
    return summary_df

if __name__ == "__main__":
    # Excel dosya yolu
    excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\Vestel PLAN_2.xlsx"
    
    # Özet tabloyu oluştur
    summary_df = create_output_summary(excel_path)
    
    # Sonuçları ekrana yazdır
    print("\nÖzet Tablo:")
    print(summary_df.to_string(index=False))
