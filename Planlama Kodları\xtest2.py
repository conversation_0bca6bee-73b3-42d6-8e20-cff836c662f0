from itertools import combinations
import math
import pandas as pd
import time
import multiprocessing
from functools import partial
import os

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\örnek1.xlsx"

KapasiteTır = 20275
KapasiteKamyon = 11059

UgramaTır = 1500
UgramaKamyon = 1000

# Yardımcı fonksiyonlar
def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 3:
        return False
    if not plaka_set:
        return True
    return any(plaka_set.issubset(rule) for rule in allowed_rules)

def coalition_digit_count(assignment, coalition, secim, customers):
    non_z_choices = [c for c in secim if c != 'z']
    xywq_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_z_choices}
    z_count = sum(1 for i in coalition if assignment[i] == 'z')
    return len(xywq_set) + z_count

def valid_coalition_digits(assignment, coalition, secim, customers, allowed_rules):
    non_z_choices = [c for c in secim if c != 'z']
    xywq_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_z_choices}
    z_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] == 'z'}
    total_count = len(xywq_set) + len(z_set)
    if total_count > 3:
        return False
    union_set = xywq_set.union(z_set)
    return any(union_set.issubset(rule) for rule in allowed_rules)

def coalition_size(assignment, coalition, secim, customers):
    return coalition_digit_count(assignment, coalition, secim, customers)

def compute_coalition_min_cost(coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    
    # Hızlı kapasite kontrolü
    if total_ldm <= KapasiteKamyon:
        K_type = "kamyon"
        sabit_maliyet = UgramaKamyon
    else:
        K_type = "tır"
        sabit_maliyet = UgramaTır
    
    # Sadece büyük gruplar için optimizasyonlar
    use_optimizations = n > 1
    
    if use_optimizations:
        # Kritik müşterileri önce işle (full truck ve az seçeneği olanlar)
        coalition_list.sort(key=lambda i: (
            customers[i].get("is_full_truck", False),
            len([opt for opt in secim if opt in customers[i]])
        ))

    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment, chosen_K
        
        # Erken budama: Mevcut maliyet en iyiyi geçtiyse dur
        current_size = coalition_size(current_assignment, coalition_list[:idx], secim, customers)
        current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost >= best_cost:
            return
        
        if idx == n:
            if valid_coalition_digits(current_assignment, coalition_list, secim, customers, allowed_rules):
                c_size = coalition_size(current_assignment, coalition_list, secim, customers)
                cost = current_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                if cost < best_cost:
                    best_cost = cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
            return
        
        customer_idx = coalition_list[idx]
        p = customers[customer_idx]
        
        # Seçenekleri belirle
        options = ['z'] if p.get("is_full_truck", False) else secim
        
        # Plaka optimizasyonu: 3 plaka dolduysa sadece mevcutları kullanan seçenekler
        if len(current_plaka_set) >= 3:
            options = [opt for opt in options if opt in p and p[opt]["plaka"] in current_plaka_set]
        
        for option in options:
            if option not in p:
                continue
                
            new_plaka = p[option]["plaka"]
            new_plaka_set = current_plaka_set | {new_plaka}
            
            # Plaka seti kontrolü
            if len(new_plaka_set) > 3 or not valid_plaka_set(new_plaka_set, allowed_rules):
                continue
                
            plaka_fiyat = p[option][K_type]
            new_max_K = max(current_max_K, plaka_fiyat)
            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]
    
    backtrack(0, {}, 0, 0, set())
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K

def get_valid_masks(N, customers, KapasiteTır):
    return [mask for mask in range(1, 1 << N) if valid_ldm([i for i in range(N) if mask & (1 << i)], customers, KapasiteTır)]

def reconstruct(mask, partition_arr):
    parts = []
    while mask:
        prev_mask, sub = partition_arr[mask]
        parts.append(sub)
        mask = prev_mask
    return parts[::-1]

def clear_output_sheet(excel_path):
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        print(f"Output sekmesi oluşturuluyor: {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def process_group(tarih, group_df, secim, print_to_terminal, truck_prices, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    sonuclar_grup = []
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (Process ID: {os.getpid()})")
    group_start_time = time.time()
    
    customers = []
    for _, row in group_df.iterrows():
        ldm_value = row["ldm"]
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": row.get("is_full_truck", False)
        }
        
        for option in secim:
            if option in ['x', 'y', 'z', 'w', 'q']:
                plaka_col = f"plaka {option}"
                tldesi_col = f"tldesi {option}"
                
                if plaka_col in row and not pd.isna(row[plaka_col]):
                    plaka = str(int(row[plaka_col])).zfill(3)
                    customer[option] = {
                        "plaka": plaka,
                        "kamyon": truck_prices.get(plaka, {}).get('kamyon', 0),
                        "tır": truck_prices.get(plaka, {}).get('tır', 0),
                        "tldesi": row.get(tldesi_col, 0)
                    }
                    
        customers.append(customer)

    N = len(customers)
    valid_masks = get_valid_masks(N, customers, KapasiteTır)
    valid_coalitions = {}

    # Koalisyon boyutu sınırı
    MAX_COALITION_SIZE = 30
    valid_masks = [mask for mask in valid_masks if bin(mask).count("1") <= MAX_COALITION_SIZE]

    for mask in valid_masks:
        coalition = [i for i in range(N) if mask & (1 << i)]
        cost, assignment, K_used = compute_coalition_min_cost(
            coalition, customers, secim, allowed_rules, 
            KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon
        )
        if cost is not None:
            valid_coalitions[mask] = (cost, assignment, K_used)

    dp = [math.inf] * (1 << N)
    partition_arr = [None] * (1 << N)
    dp[0] = 0

    # DP'yi büyük maskelerden küçüklere doğru işle
    for mask in sorted(range(1, 1 << N), key=lambda x: bin(x).count("1")):
        if dp[mask] == math.inf:
            continue
            
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition_arr[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining

    final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []
    
    for sub in final_partition:
        cost, assignment, K_used = valid_coalitions[sub]
        coalition_indices = [i for i in range(N) if sub & (1 << i)]
        coalition_names = [customers[i]["alıcı"] for i in coalition_indices]
        coalition_country = [customers[i]["il"] for i in coalition_indices]
        coalition_ldm = [int(customers[i]["ldm"]) for i in coalition_indices]
        coalition_assignment = [assignment[i] for i in coalition_indices]

        assignment_details = {
            customers[i]["alıcı"]: {
                'seçim': assignment[i],
                'hacim': int(customers[i]["hacim"])
            } for i in coalition_indices
        }
        
        toplam_hacim = int(sum(customers[i]["hacim"] for i in coalition_indices))
        
        if print_to_terminal:
            print(f"Grup: {coalition_names}, Araç: {K_used}, Maliyet: {cost}")

        sonuclar_grup.append({
            'tarih': tarih,
            'grup': str(coalition_names),
            'secim': str(coalition_assignment),
            'il': str(coalition_country),
            'hacim': str(coalition_ldm),
            'atamalar': str(assignment_details),
            'arac': K_used,
            'toplam_hacim': toplam_hacim,
            'maliyet': cost
        })
    
    group_duration = time.time() - group_start_time
    print(f"Grup işlem süresi: {group_duration:.2f}s (Müşteri: {N}, Koalisyon: {len(valid_coalitions)})")
    
    return sonuclar_grup

if __name__ == '__main__':
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'
    
    secim_input = input("Kullanılacak seçenekleri virgülle girin (x,y,z,w,q): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(',')] if secim_input else ['x','y','z','w','q']
    print(f"Seçilen seçenekler: {secim}")

    output_df = clear_output_sheet(excel_path)
    total_start_time = time.time()
    
    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
    grouped_customers = [(tarih, group) for tarih, group in customers_df.groupby('tarih')]

    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {}
    for _, row in truck_prices_df.iterrows():
        plaka = str(int(row['plaka'])).zfill(3)
        truck_prices[plaka] = {'kamyon': row['kamyon'], 'tır': row['tır']}

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()

    process_group_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon
    )

    # Paralel işlem sayısını sınırla (fiziksel çekirdek sayısı kadar)
    num_cores = max(1, multiprocessing.cpu_count() - 1)
    with multiprocessing.Pool(processes=num_cores) as pool:
        results = pool.starmap(process_group_partial, grouped_customers)

    sonuclar = [item for sublist in results for item in sublist]
    total_duration = time.time() - total_start_time
    
    print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")
        
    yeni_sonuclar_df = pd.DataFrame(sonuclar)
    if not output_df.empty:
        output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
    else:
        output_df = yeni_sonuclar_df

    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        output_df.to_excel(writer, sheet_name='output', index=False)

    print("\nSonuçlar başarıyla Excel'e kaydedildi.")