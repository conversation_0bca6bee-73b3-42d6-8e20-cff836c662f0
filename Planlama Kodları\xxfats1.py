"""
FAST ROUTING – 2025-07-22
- İç-<PERSON><PERSON><PERSON> paralelleştirme
- Divide & Conquer (>=15 alıcı)
- Düzeltme: pool.map(tuple) uyumlu
"""

from __future__ import annotations

import itertools
import math
import multiprocessing
import os
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import partial
from typing import Dict, List, Optional, Tuple

import pandas as pd

# ------------------------------------------------------------------
# Sabitler
# ------------------------------------------------------------------
excel_path = r"C:\\Users\\<USER>\\OneDrive - Ho<PERSON>z Lojistik\\_slms_\\Rota_oluşturma\\ornek2.xlsx"

KapasiteTır = 17952
KapasiteKamyon = 9792
UgramaTır = 1500
UgramaKamyon = 1000

# ------------------------------------------------------------------
# Yardımcılar
# ------------------------------------------------------------------
def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 50:
        return False
    if not plaka_set:
        return True
    return any(plaka_set.issubset(rule) for rule in allowed_rules)

# ------------------------------------------------------------------
# Tek bir koalisyon için maliyet hesabı
# ------------------------------------------------------------------
def compute_single_coalition_cost(
    coalition: Tuple[int, ...],
    customers: List[Dict],
    secim: List[str],
    allowed_rules: List[set],
    KapasiteTır: int,
    KapasiteKamyon: int,
    UgramaTır: int,
    UgramaKamyon: int,
) -> Optional[Tuple[float, Dict[int, str], str]]:
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None

    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır

    choices = []
    for idx in coalition:
        cust = customers[idx]
        opts = ["s6"] if cust.get("is_full_truck", False) else secim
        choices.append([(idx, opt) for opt in opts])

    for assignment_list in itertools.product(*choices):
        assignment = dict(assignment_list)
        plaka_set = {customers[idx][opt]["plaka"] for idx, opt in assignment_list}
        if not valid_plaka_set(plaka_set, allowed_rules):
            continue

        max_K = max(customers[idx][opt][K_type] for idx, opt in assignment_list)
        hacim_loss = sum(
            customers[idx]["hacim"] * customers[idx][opt]["tldesi"]
            for idx, opt in assignment_list
        )
        c_size = len(plaka_set)
        cost = max_K + hacim_loss + (c_size - 1) * sabit_maliyet
        if cost < best_cost:
            best_cost = cost
            best_assignment = assignment
            chosen_K = K_type
    if best_cost is math.inf:
        return None
    return best_cost, best_assignment, chosen_K

# ------------------------------------------------------------------
# Divide & Conquer – büyük grupları böler
# ------------------------------------------------------------------
def split_list(lst, parts):
    k, m = divmod(len(lst), parts)
    return [lst[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(parts)]

def process_subgroup(subgroup_customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    N = len(subgroup_customers)
    valid_masks = [
        mask
        for mask in range(1, 1 << N)
        if valid_ldm([i for i in range(N) if mask & (1 << i)], subgroup_customers, KapasiteTır)
    ]

    task_fn = partial(
        compute_single_coalition_cost,
        customers=subgroup_customers,
        secim=secim,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon,
    )

    valid_coalitions = {}
    with ProcessPoolExecutor() as exe:
        future_to_mask = {
            exe.submit(task_fn, tuple(i for i in range(N) if mask & (1 << i))): mask
            for mask in valid_masks
        }
        for fut in as_completed(future_to_mask):
            mask = future_to_mask[fut]
            res = fut.result()
            if res:
                valid_coalitions[mask] = res

    dp = [math.inf] * (1 << N)
    partition_arr = [None] * (1 << N)
    dp[0] = 0

    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition_arr[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining

    def reconstruct(mask, partition_arr):
        if mask == 0:
            return []
        prev_mask, sub = partition_arr[mask]
        return reconstruct(prev_mask, partition_arr) + [sub]

    final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []
    return final_partition, subgroup_customers, valid_coalitions

# ------------------------------------------------------------------
# Ana grup işleyici – artık (tarih, group_df) tuple alıyor
# ------------------------------------------------------------------
def process_group(
    tarih_group_tuple,
    secim,
    print_to_terminal,
    truck_prices,
    allowed_rules,
    KapasiteTır,
    KapasiteKamyon,
    UgramaTır,
    UgramaKamyon,
):
    tarih, group_df = tarih_group_tuple
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (PID: {os.getpid()})")
    t0 = time.time()

    customers = []
    for _, row in group_df.iterrows():
        cust = {
            "alıcı": row["alıcı"],
            "ldm": row["ldm"],
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": False,
        }
        for s in ["s1", "s2", "s3", "s4", "s5", "s6"]:
            if s in secim and str(row.get(f"plaka {s}", "")).strip():
                plaka = str(int(row[f"plaka {s}"])).zfill(3)
                cust[s] = {
                    "plaka": plaka,
                    "kamyon": truck_prices[plaka]["kamyon"],
                    "tır": truck_prices[plaka]["tır"],
                    "tldesi": row[f"tldesi {s}"],
                }
        customers.append(cust)

    DIVIDE_THRESHOLD = 15
    if len(customers) <= DIVIDE_THRESHOLD:
        partition, customers_used, valid_coalitions = process_subgroup(
            customers,
            secim,
            allowed_rules,
            KapasiteTır,
            KapasiteKamyon,
            UgramaTır,
            UgramaKamyon,
        )
    else:
        subgroups = split_list(customers, 4)
        all_parts = []
        with ProcessPoolExecutor() as exe:
            futures = [
                exe.submit(
                    process_subgroup,
                    sg,
                    secim,
                    allowed_rules,
                    KapasiteTır,
                    KapasiteKamyon,
                    UgramaTır,
                    UgramaKamyon,
                )
                for sg in subgroups
            ]
            for fut in as_completed(futures):
                part, _, _ = fut.result()
                all_parts.extend(part)
        partition = all_parts

    sonuclar = []
    for mask in partition:
        if mask == 0:
            continue
        if len(customers) <= DIVIDE_THRESHOLD:
            cost, assignment, K_used = valid_coalitions[mask]
        else:
            coalition = tuple(i for i in range(len(customers)) if mask & (1 << i))
            res = compute_single_coalition_cost(
                coalition,
                customers,
                secim,
                allowed_rules,
                KapasiteTır,
                KapasiteKamyon,
                UgramaTır,
                UgramaKamyon,
            )
            if not res:
                continue
            cost, assignment, K_used = res

        coalition_names = [customers[i]["alıcı"] for i in range(len(customers)) if mask & (1 << i)]
        coalition_country = [customers[i]["il"] for i in range(len(customers)) if mask & (1 << i)]
        coalition_ldm = [int(customers[i]["ldm"]) for i in range(len(customers)) if mask & (1 << i)]
        toplam_hacim = int(sum(customers[i]["hacim"] for i in range(len(customers)) if mask & (1 << i)))

        assignment_details = {
            customers[i]["alıcı"]: {"seçim": assignment[i], "hacim": int(customers[i]["hacim"])}
            for i in assignment
            if mask & (1 << i)
        }

        if print_to_terminal:
            print(
                f"Grup: {coalition_names}, "
                f"Atamalar: {assignment_details}, "
                f"Araç: {K_used}, "
                f"Hacim: {toplam_hacim}, "
                f"Maliyet: {cost}"
            )

        sonuclar.append(
            {
                "tarih": tarih,
                "grup": str(coalition_names),
                "secim": str(list(assignment.values())),
                "il": str(coalition_country),
                "hacim": str(coalition_ldm),
                "atamalar": str(assignment_details),
                "arac": K_used,
                "toplam_hacim": toplam_hacim,
                "maliyet": cost,
            }
        )

    print(f"{tarih.strftime('%Y-%m-%d')} tamamlandı – {time.time() - t0:.2f}s")
    return sonuclar

# ------------------------------------------------------------------
# Ana program – tuple uyumlu
# ------------------------------------------------------------------
if __name__ == "__main__":
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == "E"
    secim_input = input("Kullanılacak seçenekleri virgülle girin (s1,s2,s3,s4,s5,s6): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(",")] if secim_input else ["s1", "s2", "s3", "s4", "s5", "s6"]
    print(f"Seçilen seçenekler: {secim}")

    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df["tarih"] = pd.to_datetime(customers_df["tarih"])
    grouped_customers = list(customers_df.groupby("tarih"))

    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {
        str(int(row["plaka"])).zfill(3): {"kamyon": row["kamyon"], "tır": row["tır"]}
        for _, row in truck_prices_df.iterrows()
    }

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = [set(str(x).split(",")) for _, x in rules_df[0].items()]

    try:
        with pd.ExcelFile(excel_path) as xls:
            if "output" in xls.sheet_names:
                if input("Mevcut 'output' sekmesini temizlemek ister misiniz? (E/H): ").strip().upper() == "E":
                    empty_df = pd.DataFrame(
                        columns=[
                            "tarih",
                            "grup",
                            "atamalar",
                            "arac",
                            "toplam_hacim",
                            "maliyet",
                            "ülke",
                            "ldm",
                        ]
                    )
                    with pd.ExcelWriter(excel_path, mode="a", if_sheet_exists="replace") as writer:
                        empty_df.to_excel(writer, sheet_name="output", index=False)
    except Exception:
        pass

    total_start = time.time()
    process_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon,
    )

    with ProcessPoolExecutor() as pool:
        results = pool.map(process_partial, grouped_customers)

    sonuclar = []
    for r in results:
        sonuclar.extend(r)

    out_df = pd.DataFrame(sonuclar)
    with pd.ExcelWriter(excel_path, mode="a", if_sheet_exists="replace") as writer:
        out_df.to_excel(writer, sheet_name="output", index=False)

    print(f"\nTüm işlem {time.time() - total_start:.2f} saniyede tamamlandı.")