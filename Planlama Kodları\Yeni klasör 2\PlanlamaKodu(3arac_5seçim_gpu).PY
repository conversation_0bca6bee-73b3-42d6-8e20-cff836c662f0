from itertools import combinations
import math
import pandas as pd
import time
import numpy as np
import sqlite3
import pickle
import hashlib
import pycuda.autoinit
import pycuda.driver as cuda
from pycuda.compiler import SourceModule
from pycuda import gpuarray

excel_path = r"C:\Users\<USER>\OneDrive - Horoz Lojistik\_slms_\Rota_oluşturma\DATA_PEYMAN.xlsx"

# Constants
KapasiteTır = 20275
KapasiteKamyon = 11059
UgramaTır = 1500
UgramaKamyon = 1000
MAX_COALITION_SIZE = 8  # Reasonable size for GPU computation

print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'

# Seçim parametresini al
secim_input = input("Kullanılacak seçenekleri virgülle girin (x,y,z,w,q): ").strip().lower()
secim = [s.strip() for s in secim_input.split(',')] if secim_input else ['x','y','z','w','q']
print(f"Seçilen seçenekler: {secim}")

# Tüm işlemlerin toplam süresi için
total_start_time = time.time()

# Veritabanı bağlantısı
conn = sqlite3.connect('coalition_cache.db')
c = conn.cursor()
c.execute('''CREATE TABLE IF NOT EXISTS coalitions
             (hash TEXT PRIMARY KEY, cost REAL, assignment BLOB, k_type TEXT)''')
conn.commit()

def get_coalition_hash(coalition):
    """Koalisyon için benzersiz hash oluştur"""
    return hashlib.md5(str(coalition).encode()).hexdigest()

def cache_coalition(coalition, cost, assignment, k_type):
    """Koalisyon sonucunu önbelleğe al"""
    hash = get_coalition_hash(coalition)
    c.execute("INSERT OR REPLACE INTO coalitions VALUES (?, ?, ?, ?)",
              (hash, cost, sqlite3.Binary(pickle.dumps(assignment)), k_type))
    conn.commit()

def get_cached_coalition(coalition):
    """Önbellekten koalisyon sonucunu al"""
    hash = get_coalition_hash(coalition)
    c.execute("SELECT cost, assignment, k_type FROM coalitions WHERE hash=?", (hash,))
    row = c.fetchone()
    if row:
        return row[0], pickle.loads(row[1]), row[2]
    return None

# CUDA kernel kodu
cuda_code = """
#include <stdio.h>

__device__ float atomicMinFloat(float* address, float val) {
    int* address_as_i = (int*) address;
    int old = *address_as_i, assumed;
    do {
        assumed = old;
        old = atomicCAS(address_as_i, assumed, __float_as_int(fminf(val, __int_as_float(assumed))));
    } while (assumed != old);
    return __int_as_float(old);
}

__global__ void compute_coalition_costs(
    float* results, 
    int* assignments, 
    float* customer_ldm, 
    float* customer_hacim,
    int* customer_plaka_indices,
    float* option_prices,
    float* option_tldesi,
    int* rule_masks,
    int num_rules,
    int num_options,
    int num_customers,
    int kapasite_kamyon,
    int kapasite_tir,
    int ugrama_kamyon,
    int ugrama_tir
) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int coalition_size = blockIdx.y + 1;  // Coalition size (1 to MAX_COALITION_SIZE)
    int total_coalitions = num_customers;  // Simplified for this example
    
    if (idx >= total_coalitions) return;
    
    // This is a simplified version - real implementation would handle multiple customers
    int customer_idx = idx;
    
    // Calculate total LDM for coalition
    float total_ldm = customer_ldm[customer_idx];
    
    // Determine vehicle type
    char k_type = (total_ldm <= kapasite_kamyon) ? 0 : 1;  // 0=kamyon, 1=tır
    int sabit_maliyet = (k_type == 0) ? ugrama_kamyon : ugrama_tir;
    
    float best_cost = INFINITY;
    int best_option = -1;
    
    // Try all options for this customer
    for (int option = 0; option < num_options; option++) {
        float plaka_fiyat = option_prices[option * num_customers + customer_idx];
        float hacim_loss = customer_hacim[customer_idx] * option_tldesi[option * num_customers + customer_idx];
        
        // Cost calculation
        float cost = plaka_fiyat + hacim_loss;
        
        // Update best cost
        if (cost < best_cost) {
            best_cost = cost;
            best_option = option;
        }
    }
    
    // Store results
    if (best_option != -1) {
        results[idx] = best_cost;
        assignments[idx] = best_option;
    }
}
"""

# CUDA modülünü derle
mod = SourceModule(cuda_code)
compute_coalition_costs = mod.get_function("compute_coalition_costs")

def compute_coalition_min_cost_gpu(coalition, customers, truck_prices, allowed_rules):
    """Koalisyon maliyetini GPU'da hesapla"""
    # Önbellekten kontrol et
    cached = get_cached_coalition(tuple(coalition))
    if cached:
        return cached
    
    n = len(coalition)
    if n > MAX_COALITION_SIZE:
        # Büyük koalisyonlar için farklı bir yöntem kullan
        return compute_coalition_min_cost_cpu(coalition, customers)
    
    # Müşteri verilerini GPU'ya uygun formata getir
    customer_ldm = np.array([customers[i]["ldm"] for i in coalition], dtype=np.float32)
    customer_hacim = np.array([customers[i]["hacim"] for i in coalition], dtype=np.float32)
    
    # Seçenek verilerini hazırla
    num_options = len(secim)
    option_prices = np.zeros((num_options, n), dtype=np.float32)
    option_tldesi = np.zeros((num_options, n), dtype=np.float32)
    customer_plaka_indices = np.zeros((num_options, n), dtype=np.int32)
    
    # Plaka indeksleme için sözlük
    plaka_to_index = {}
    index_counter = 0
    
    for i, customer_idx in enumerate(coalition):
        customer = customers[customer_idx]
        for j, option in enumerate(secim):
            if option in customer:
                option_data = customer[option]
                option_prices[j, i] = option_data['kamyon']  # Varsayılan olarak kamyon
                option_tldesi[j, i] = option_data['tldesi']
                
                # Plakayı indeksle
                plaka = option_data['plaka']
                if plaka not in plaka_to_index:
                    plaka_to_index[plaka] = index_counter
                    index_counter += 1
                customer_plaka_indices[j, i] = plaka_to_index[plaka]
    
    # Kuralları GPU'ya uygun formata getir
    rule_masks = []
    for rule in allowed_rules:
        rule_mask = 0
        for plaka in rule:
            if plaka in plaka_to_index:
                rule_mask |= (1 << plaka_to_index[plaka])
        rule_masks.append(rule_mask)
    
    rule_masks = np.array(rule_masks, dtype=np.int32)
    
    # GPU için bellek ayır
    d_results = gpuarray.zeros(n, dtype=np.float32)
    d_assignments = gpuarray.zeros(n, dtype=np.int32)
    d_customer_ldm = gpuarray.to_gpu(customer_ldm)
    d_customer_hacim = gpuarray.to_gpu(customer_hacim)
    d_option_prices = gpuarray.to_gpu(option_prices.flatten())
    d_option_tldesi = gpuarray.to_gpu(option_tldesi.flatten())
    d_customer_plaka_indices = gpuarray.to_gpu(customer_plaka_indices.flatten())
    d_rule_masks = gpuarray.to_gpu(rule_masks)
    
    # GPU parametreleri
    block_size = 256
    grid_size = (n + block_size - 1) // block_size
    
    # GPU hesaplamayı başlat
    compute_coalition_costs(
        d_results, d_assignments, d_customer_ldm, d_customer_hacim,
        d_customer_plaka_indices, d_option_prices, d_option_tldesi,
        d_rule_masks, np.int32(len(rule_masks)), np.int32(num_options),
        np.int32(n), np.int32(KapasiteKamyon), np.int32(KapasiteTır),
        np.int32(UgramaKamyon), np.int32(UgramaTır),
        block=(block_size, 1, 1), grid=(grid_size, 1)
    )
    
    # Sonuçları GPU'dan al
    results = d_results.get()
    assignments = d_assignments.get()
    
    # En iyi maliyeti ve atamayı bul
    best_cost = np.min(results)
    best_idx = np.argmin(results)
    best_option_idx = assignments[best_idx]
    
    # Atamayı oluştur
    assignment_dict = {}
    for i, customer_idx in enumerate(coalition):
        if i == best_idx:
            assignment_dict[customer_idx] = secim[best_option_idx]
        else:
            # Diğer müşteriler için varsayılan atama
            assignment_dict[customer_idx] = secim[0]
    
    # Araç tipini belirle
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    k_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    
    # Sonucu önbelleğe al
    cache_coalition(tuple(coalition), float(best_cost), assignment_dict, k_type)
    
    return best_cost, assignment_dict, k_type

def compute_coalition_min_cost_cpu(coalition, customers):
    """Büyük koalisyonlar için CPU tabanlı hesaplama"""
    # Basitleştirilmiş bir yaklaşım - gerçek uygulama daha karmaşık olmalı
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    k_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    
    # Her müşteri için en ucuz seçeneği bul
    assignment = {}
    total_cost = 0
    
    for i in coalition:
        min_cost = float('inf')
        best_option = None
        
        for option in secim:
            if option in customers[i]:
                cost = customers[i][option]['kamyon'] if k_type == "kamyon" else customers[i][option]['tır']
                if cost < min_cost:
                    min_cost = cost
                    best_option = option
        
        if best_option:
            assignment[i] = best_option
            total_cost += min_cost
    
    # Sabit maliyetleri ekle
    fixed_cost = UgramaKamyon if k_type == "kamyon" else UgramaTır
    total_cost += fixed_cost * (len(assignment) - 1)
    
    return total_cost, assignment, k_type

def clear_output_sheet():
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        print(f"Output sekmesi oluşturuluyor: {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def valid_ldm(coalition):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def get_valid_masks(N):
    """Geçerli koalisyon maskelerini oluştur"""
    masks = []
    for size in range(1, min(MAX_COALITION_SIZE, N) + 1):
        for combo in combinations(range(N), size):
            mask = sum(1 << i for i in combo)
            if valid_ldm(combo):
                masks.append(mask)
    return masks

# Ana işlem
output_df = clear_output_sheet()

truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
truck_prices = {}
for _, row in truck_prices_df.iterrows():
    plaka = str(int(row['plaka'])).zfill(3)
    truck_prices[plaka] = {
        'kamyon': row['kamyon'],
        'tır': row['tır']
    }

customers_df = pd.read_excel(excel_path, sheet_name="customers")
customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
grouped_customers = customers_df.groupby('tarih')

sonuclar = []
for tarih, group_df in grouped_customers:
    group_start_time = time.time()
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor...")
    
    # Müşteri verilerini yükle
    customers = []
    for _, row in group_df.iterrows():
        ldm_value = row["ldm"]
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": False
        }
        
        # Seçenekleri ekle
        if 'x' in secim:
            plaka_x = str(int(row["plaka x"])).zfill(3)
            customer["x"] = {
                "plaka": plaka_x,
                "kamyon": truck_prices[plaka_x]['kamyon'],
                "tır": truck_prices[plaka_x]['tır'],
                "tldesi": row["tldesi x"]
            }
        
        if 'y' in secim:
            plaka_y = str(int(row["plaka y"])).zfill(3)
            customer["y"] = {
                "plaka": plaka_y,
                "kamyon": truck_prices[plaka_y]['kamyon'],
                "tır": truck_prices[plaka_y]['tır'],
                "tldesi": row["tldesi y"]
            }
        
        if 'z' in secim:
            plaka_z = str(int(row["plaka z"])).zfill(3)
            customer["z"] = {
                "plaka": plaka_z,
                "kamyon": truck_prices[plaka_z]['kamyon'],
                "tır": truck_prices[plaka_z]['tır'],
                "tldesi": row["tldesi z"]
            }
        
        if 'w' in secim:
            plaka_w = str(int(row["plaka w"])).zfill(3)
            customer["w"] = {
                "plaka": plaka_w,
                "kamyon": truck_prices[plaka_w]['kamyon'],
                "tır": truck_prices[plaka_w]['tır'],
                "tldesi": row["tldesi w"]
            }
        
        if 'q' in secim:
            plaka_q = str(int(row["plaka q"])).zfill(3)
            customer["q"] = {
                "plaka": plaka_q,
                "kamyon": truck_prices[plaka_q]['kamyon'],
                "tır": truck_prices[plaka_q]['tır'],
                "tldesi": row["tldesi q"]
            }
        
        customers.append(customer)
    
    # Kuralları yükle
    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()
    
    N = len(customers)
    valid_masks = get_valid_masks(N)
    valid_coalitions = {}
    
    # Koalisyonları GPU'da hesapla
    print(f"  {len(valid_masks)} koalisyon GPU'da hesaplanıyor...")
    gpu_start = time.time()
    
    for mask in valid_masks:
        coalition = [i for i in range(N) if mask & (1 << i)]
        cost, assignment, K_used = compute_coalition_min_cost_gpu(coalition, customers, truck_prices, allowed_rules)
        if cost is not None:
            valid_coalitions[mask] = (cost, assignment, K_used)
    
    gpu_duration = time.time() - gpu_start
    print(f"  GPU hesaplama süresi: {gpu_duration:.2f} saniye")
    
    # Dinamik programlama ile en iyi bölümlemeyi bul
    dp = [math.inf] * (1 << N)
    partition = [None] * (1 << N)
    dp[0] = 0
    
    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining
    
    # Sonuçları topla
    final_mask = (1 << N) - 1
    if dp[final_mask] == math.inf:
        print("  Uygun çözüm bulunamadı!")
        continue
    
    # Bölümlemeyi yeniden oluştur
    partitions = []
    mask = final_mask
    while mask:
        prev_mask, sub = partition[mask]
        partitions.append(sub)
        mask = prev_mask
    
    # Sonuçları işle
    for sub in partitions:
        cost, assignment, K_used = valid_coalitions[sub]
        coalition_names = [customers[i]["alıcı"] for i in range(N) if sub & (1 << i)]
        coalition_country = [customers[i]["il"] for i in range(N) if sub & (1 << i)]
        coalition_ldm = [int(customers[i]["ldm"]) for i in range(N) if sub & (1 << i)]
        coalition_assignment = [assignment[i] for i in range(N) if sub & (1 << i)]
        
        assignment_details = {}
        for i in assignment:
            if sub & (1 << i):
                assignment_details[customers[i]["alıcı"]] = {
                    'seçim': assignment[i],
                    'hacim': int(customers[i]["hacim"])
                }
        
        toplam_hacim = int(sum(customers[i]["hacim"] for i in range(N) if sub & (1 << i)))
        
        if print_to_terminal:
            print(f"Grup: {coalition_names}, Grup Atamaları: {assignment_details}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim}, Grup Maliyeti: {cost}")
        
        sonuclar.append({
            'tarih': tarih,
            'grup': str(coalition_names),
            'secim': str(coalition_assignment),
            'il': str(coalition_country),
            'hacim': str(coalition_ldm),
            'atamalar': str(assignment_details),
            'arac': K_used,
            'toplam_hacim': toplam_hacim,
            'maliyet': cost
        })
    
    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Bu grup işlemi {group_duration:.2f} saniye sürdü")

# Toplam süre hesaplama
total_duration = time.time() - total_start_time
if print_to_terminal:
    print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")

# Veritabanını kapat
conn.close()

# Sonuçları Excel'e yaz
yeni_sonuclar_df = pd.DataFrame(sonuclar)
if not output_df.empty:
    output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
else:
    output_df = yeni_sonuclar_df

with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    output_df.to_excel(writer, sheet_name='output', index=False)

print("\nSonuçlar başarıyla Excel'e kaydedildi.")