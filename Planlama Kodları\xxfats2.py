"""
HIZLI GERİ DÖNÜŞ – 2025-07-22
- <PERSON><PERSON><PERSON><PERSON><PERSON> gruplarda (<12) ilk kodun backtrack'ini kullan
- 12+ alıcı varsa D&C + paralel
"""

from itertools import combinations
import math
import pandas as pd
import time
import multiprocessing
from functools import partial
import os
from concurrent.futures import ProcessPoolExecutor, as_completed

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\ornek2.xlsx"

KapasiteTır = 17952
KapasiteKamyon = 9792
UgramaTır = 1500
UgramaKamyon = 1000

# ---------- orijinal yardımcılar ----------
def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 50:
        return False
    if not plaka_set:
        return True
    return any(plaka_set.issubset(rule) for rule in allowed_rules)

def coalition_digit_count(assignment, coalition, secim, customers):
    non_s6 = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def valid_coalition_digits(assignment, coalition, secim, customers, allowed_rules):
    non_s6 = [c for c in secim if c != 's6']
    s_set  = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6}
    s6_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] == 's6'}
    union  = s_set.union(s6_set)
    return len(union) <= 50 and any(union.issubset(rule) for rule in allowed_rules)

# ---------- orijinal backtrack ----------
def compute_coalition_min_cost(coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır

    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment, chosen_K
        current_size = coalition_digit_count(current_assignment, coalition_list[:idx], secim, customers)
        current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost >= best_cost:
            return
        if idx == n:
            if valid_coalition_digits(current_assignment, coalition_list, secim, customers, allowed_rules):
                c_size = coalition_digit_count(current_assignment, coalition_list, secim, customers)
                cost = current_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                if cost < best_cost:
                    best_cost = cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
            return
        if not valid_plaka_set(current_plaka_set, allowed_rules):
            return
        customer_idx = coalition_list[idx]
        p = customers[customer_idx]
        options = ['s6'] if p.get("is_full_truck", False) else secim
        for option in sorted(options, key=lambda opt: p[opt][K_type]):  # küçükten büyüğe
            new_plaka = p[option]["plaka"]
            new_plaka_set = current_plaka_set | {new_plaka}
            if not valid_plaka_set(new_plaka_set, allowed_rules):
                continue
            plaka_fiyat = p[option][K_type]
            new_max_K = max(current_max_K, plaka_fiyat)
            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]

    backtrack(0, {}, 0, 0, set())
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K

def get_valid_masks(N, customers, KapasiteTır):
    return [mask for mask in range(1, 1 << N) if valid_ldm([i for i in range(N) if mask & (1 << i)], customers, KapasiteTır)]

def reconstruct(mask, partition_arr):
    if mask == 0:
        return []
    prev_mask, sub = partition_arr[mask]
    return reconstruct(prev_mask, partition_arr) + [sub]

# ---------- küçük grup (orijinal algoritma) ----------
def process_small_group(customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    N = len(customers)
    valid_masks = get_valid_masks(N, customers, KapasiteTır)
    valid_coalitions = {}
    for mask in valid_masks:
        coalition = [i for i in range(N) if mask & (1 << i)]
        cost, assignment, K_used = compute_coalition_min_cost(
            coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon
        )
        if cost is not None:
            valid_coalitions[mask] = (cost, assignment, K_used)

    dp = [math.inf] * (1 << N)
    partition_arr = [None] * (1 << N)
    dp[0] = 0
    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition_arr[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining
    final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []
    return final_partition, valid_coalitions

# ---------- büyük grup (D&C) ----------
def split_list(lst, parts):
    k, m = divmod(len(lst), parts)
    return [lst[i*k + min(i,m):(i+1)*k + min(i+1,m)] for i in range(parts)]

def process_big_group(customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    # Basitçe 4 parçaya böl
    subgroups = split_list(customers, 4)
    partitions = []
    with ProcessPoolExecutor() as exe:
        futures = [exe.submit(process_small_group, sg, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon) for sg in subgroups]
        for fut in as_completed(futures):
            part, _ = fut.result()
            partitions.extend(part)
    return partitions

# ---------- grup işleyici ----------
def process_group(
    tarih_group_tuple,
    secim,
    print_to_terminal,
    truck_prices,
    allowed_rules,
    KapasiteTır,
    KapasiteKamyon,
    UgramaTır,
    UgramaKamyon,
):
    tarih, group_df = tarih_group_tuple
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (PID: {os.getpid()})")
    t0 = time.time()

    customers = []
    for _, row in group_df.iterrows():
        cust = {
            "alıcı": row["alıcı"],
            "ldm": row["ldm"],
            "hacim": row["hacim"],
            "il": row["data_il"],
            "is_full_truck": False,
        }
        for s in ["s1","s2","s3","s4","s5","s6"]:
            if s in secim and str(row.get(f"plaka {s}", "")).strip():
                plaka = str(int(row[f"plaka {s}"])).zfill(3)
                cust[s] = {
                    "plaka": plaka,
                    "kamyon": truck_prices[plaka]["kamyon"],
                    "tır": truck_prices[plaka]["tır"],
                    "tldesi": row[f"tldesi {s}"],
                }
        customers.append(cust)

    # Eşik: 12 alıcı
    if len(customers) <= 12:
        final_partition, valid_coalitions = process_small_group(
            customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon
        )
    else:
        final_partition = process_big_group(
            customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon
        )

    sonuclar = []
    N = len(customers)
    for mask in final_partition:
        if len(customers) <= 12:
            cost, assignment, K_used = valid_coalitions[mask]
        else:
            # büyük grup için tekrar hesapla
            coalition = [i for i in range(N) if mask & (1 << i)]
            cost, assignment, K_used = compute_coalition_min_cost(
                coalition, customers, secim, allowed_rules, KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon
            )
            if cost is None:
                continue

        coalition_names = [customers[i]["alıcı"] for i in range(N) if mask & (1 << i)]
        coalition_country = [customers[i]["il"] for i in range(N) if mask & (1 << i)]
        coalition_ldm = [int(customers[i]["ldm"]) for i in range(N) if mask & (1 << i)]
        toplam_hacim = int(sum(customers[i]["hacim"] for i in range(N) if mask & (1 << i)))

        assignment_details = {
            customers[i]["alıcı"]: {"seçim": assignment[i], "hacim": int(customers[i]["hacim"])}
            for i in assignment
            if mask & (1 << i)
        }

        if print_to_terminal:
            print(
                f"Grup: {coalition_names}, "
                f"Atamalar: {assignment_details}, "
                f"Araç: {K_used}, "
                f"Hacim: {toplam_hacim}, "
                f"Maliyet: {cost}"
            )

        sonuclar.append(
            {
                "tarih": tarih,
                "grup": str(coalition_names),
                "secim": str(list(assignment.values())),
                "il": str(coalition_country),
                "hacim": str(coalition_ldm),
                "atamalar": str(assignment_details),
                "arac": K_used,
                "toplam_hacim": toplam_hacim,
                "maliyet": cost,
            }
        )
    print(f"{tarih.strftime('%Y-%m-%d')} tamamlandı – {time.time() - t0:.2f}s")
    return sonuclar

# ------------------------------------------------------------------
# main
# ------------------------------------------------------------------
if __name__ == "__main__":
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == "E"
    secim_input = input("Kullanılacak seçenekleri virgülle girin (s1,s2,s3,s4,s5,s6): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(",")] if secim_input else ["s1","s2","s3","s4","s5","s6"]
    print(f"Seçilen seçenekler: {secim}")

    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df["tarih"] = pd.to_datetime(customers_df["tarih"])
    grouped_customers = list(customers_df.groupby("tarih"))

    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {
        str(int(row["plaka"])).zfill(3): {"kamyon": row["kamyon"], "tır": row["tır"]}
        for _, row in truck_prices_df.iterrows()
    }

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = [set(str(x).split(",")) for _, x in rules_df[0].items()]

    total_start = time.time()
    process_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon,
    )

    with multiprocessing.Pool() as pool:
        results = pool.map(process_partial, grouped_customers)

    sonuclar = [item for sublist in results for item in sublist]
    out_df = pd.DataFrame(sonuclar)
    with pd.ExcelWriter(excel_path, mode="a", if_sheet_exists="replace") as writer:
        out_df.to_excel(writer, sheet_name="output", index=False)

    print(f"\nTüm işlem {time.time() - total_start:.2f} saniyede tamamlandı.")