#include <iostream>
#include <fstream>
#include <vector>
#include <map>
#include <set>
#include <cmath>
#include <algorithm>
#include <ctime>
#include <thread>
#include <mutex>
#include <sstream>
#include <iomanip>
#include <limits>
#include <unordered_map>
#include <bitset>
#include <future>
#include "xlsxwriter.h"
#include "xlnt/xlnt.hpp"

using namespace std;
using namespace xlnt;

const double KapasiteTir = 1000.0;
const double KapasiteKamyon = 500.0;
const double UgramaTir = 1500.0;
const double UgramaKamyon = 1000.0;

struct TruckPrice {
    double kamyon;
    double tir;
};

struct CustomerOption {
    string plaka;
    double kamyon;
    double tir;
    double tldesi;
};

struct Customer {
    string alici;
    double ldm;
    double hacim;
    string il;
    bool is_full_truck;
    map<char, CustomerOption> options;
};

struct CoalitionResult {
    double cost;
    map<size_t, char> assignment;
    string vehicle_type;
};

struct GroupResult {
    string tarih;
    string grup;
    string secim;
    string il;
    string hacim;
    string atamalar;
    string arac;
    double toplam_hacim;
    double maliyet;
};

// Helper functions
bool valid_ldm(const vector<size_t>& coalition, const vector<Customer>& customers, double capacity) {
    double total = 0.0;
    for (auto i : coalition) {
        total += customers[i].ldm;
    }
    return total <= capacity;
}

bool valid_plaka_set(const set<string>& plaka_set, const vector<set<string>>& allowed_rules) {
    if (plaka_set.size() > 3) return false;
    if (plaka_set.empty()) return true;
    
    for (const auto& rule : allowed_rules) {
        bool is_subset = true;
        for (const auto& plaka : plaka_set) {
            if (rule.find(plaka) == rule.end()) {
                is_subset = false;
                break;
            }
        }
        if (is_subset) return true;
    }
    return false;
}

int coalition_digit_count(const map<size_t, char>& assignment, const vector<size_t>& coalition, 
                         const vector<char>& secim, const vector<Customer>& customers) {
    set<string> non_z_plates;
    int z_count = 0;
    
    for (auto i : coalition) {
        if (assignment.find(i) != assignment.end()) {
            char choice = assignment.at(i);
            if (choice != 'z') {
                non_z_plates.insert(customers[i].options.at(choice).plaka);
            } else {
                z_count++;
            }
        }
    }
    return non_z_plates.size() + z_count;
}

bool valid_coalition_digits(const map<size_t, char>& assignment, const vector<size_t>& coalition,
                           const vector<char>& secim, const vector<Customer>& customers,
                           const vector<set<string>>& allowed_rules) {
    set<string> plates;
    for (auto i : coalition) {
        if (assignment.find(i) != assignment.end()) {
            char choice = assignment.at(i);
            plates.insert(customers[i].options.at(choice).plaka);
        }
    }
    return plates.size() <= 3 && valid_plaka_set(plates, allowed_rules);
}

vector<size_t> get_valid_masks(size_t N, const vector<Customer>& customers) {
    vector<size_t> valid_masks;
    for (size_t mask = 1; mask < (1 << N); ++mask) {
        vector<size_t> coalition;
        for (size_t i = 0; i < N; ++i) {
            if (mask & (1 << i)) {
                coalition.push_back(i);
            }
        }
        if (valid_ldm(coalition, customers, KapasiteTir)) {
            valid_masks.push_back(mask);
        }
    }
    return valid_masks;
}

CoalitionResult compute_coalition_min_cost(const vector<size_t>& coalition, const vector<Customer>& customers,
                                          const vector<char>& secim, const vector<set<string>>& allowed_rules) {
    CoalitionResult best_result;
    best_result.cost = numeric_limits<double>::max();
    
    double total_ldm = 0.0;
    for (auto i : coalition) {
        total_ldm += customers[i].ldm;
    }
    string K_type = (total_ldm <= KapasiteKamyon) ? "kamyon" : "tir";
    double sabit_maliyet = (K_type == "kamyon") ? UgramaKamyon : UgramaTir;
    
    // Backtracking implementation here
    // (Actual implementation would mirror Python's backtracking logic)
    
    return best_result;
}

vector<GroupResult> process_group(const string& tarih, const vector<Customer>& group_customers,
                                 const vector<char>& secim, bool print_to_terminal,
                                 const map<string, TruckPrice>& truck_prices,
                                 const vector<set<string>>& allowed_rules) {
    vector<GroupResult> sonuclar_grup;
    // Main processing logic here
    return sonuclar_grup;
}

int main() {
    // Read Excel file
    xlnt::workbook wb;
    wb.load("mersinplanlama.xlsx");
    
    // Read truck prices
    map<string, TruckPrice> truck_prices;
    auto truck_sheet = wb.sheet_by_title("truckprice");
    for (auto row : truck_sheet.rows()) {
        string plaka = to_string(row[0].to_long());
        TruckPrice price{row[1].to_double(), row[2].to_double()};
        truck_prices[plaka] = price;
    }
    
    // Read allowed rules
    vector<set<string>> allowed_rules;
    auto rules_sheet = wb.sheet_by_title("allowed_rules");
    for (auto row : rules_sheet.rows()) {
        set<string> rule;
        string rule_str = row[0].to_string();
        // Parse comma-separated values
        allowed_rules.push_back(rule);
    }
    
    // Read customers data
    vector<Customer> customers;
    auto customer_sheet = wb.sheet_by_title("customers");
    for (auto row : customer_sheet.rows()) {
        Customer c;
        // Populate customer data
        customers.push_back(c);
    }
    
    // Group customers by date
    map<string, vector<Customer>> grouped_customers;
    for (const auto& c : customers) {
        // Group by date (implementation depends on data structure)
    }
    
    // Process groups in parallel
    vector<future<vector<GroupResult>>> futures;
    for (const auto& group : grouped_customers) {
        futures.push_back(async(launch::async, process_group, 
                              group.first, group.second, secim, 
                              print_to_terminal, truck_prices, allowed_rules));
    }
    
    // Collect results
    vector<GroupResult> all_results;
    for (auto& fut : futures) {
        auto group_results = fut.get();
        all_results.insert(all_results.end(), group_results.begin(), group_results.end());
    }
    
    // Write results to Excel
    workbook out_wb;
    worksheet out_ws = out_wb.add_worksheet("output");
    
    // Write headers
    vector<string> headers = {"tarih", "grup", "secim", "il", "hacim", "atamalar", "arac", "toplam_hacim", "maliyet"};
    for (size_t i = 0; i < headers.size(); ++i) {
        out_ws.cell(1, i+1).value(headers[i]);
    }
    
    // Write data
    for (size_t i = 0; i < all_results.size(); ++i) {
        const auto& res = all_results[i];
        out_ws.cell(i+2, 1).value(res.tarih);
        out_ws.cell(i+2, 2).value(res.grup);
        // ... other fields
    }
    
    out_wb.save("mersinplanlama_updated.xlsx");
    return 0;
}