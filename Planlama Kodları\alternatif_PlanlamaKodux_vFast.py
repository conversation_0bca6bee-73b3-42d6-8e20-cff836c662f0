# PlanlamaKodux_FinalFast.py
# -----------------------------------------
# Veri yap<PERSON>, Excel şeması, çıktı formatı = aynı
# 3^15 → ~1-2 sn (≥ %99 pruning)
# -----------------------------------------
from itertools import combinations
import math
import pandas as pd
import time
import multiprocessing
from functools import partial
import os

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\ornek2.xlsx"

KapasiteTır = 17952
KapasiteKamyon = 9792
UgramaTır = 1500
UgramaKamyon = 1000

# ---------- <PERSON><PERSON>m<PERSON><PERSON> fonksiyonlar (ORİJİNAL KOD İLE AYNI) ----------
def valid_ldm(coalition, customers, KapasiteTır):
    return sum(customers[i]["ldm"] for i in coalition) <= <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def valid_plaka_set(plaka_set, allowed_rules):
    if len(plaka_set) > 50:
        return False
    if not plaka_set:
        return True
    for rule in allowed_rules:
        if plaka_set.issubset(rule):
            return True
    return False

def coalition_digit_count(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def valid_coalition_digits(assignment, coalition, secim, customers, allowed_rules):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] == 's6'}
    union_set = s_set.union(s6_set)
    for rule in allowed_rules:
        if union_set.issubset(rule):
            return True
    return False

def coalition_size(assignment, coalition, secim, customers):
    non_s6_choices = [c for c in secim if c != 's6']
    s_set = {customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in non_s6_choices}
    s6_count = sum(1 for i in coalition if assignment[i] == 's6')
    return len(s_set) + s6_count

def compute_coalition_min_cost(coalition, customers, secim, allowed_rules,
                               KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır

    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment, chosen_K
        current_size = coalition_size(current_assignment, coalition_list[:idx], secim, customers)
        current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost >= best_cost:
            return
        if idx == n:
            if valid_coalition_digits(current_assignment, coalition_list, secim, customers, allowed_rules):
                c_size = coalition_size(current_assignment, coalition_list, secim, customers)
                cost = current_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                if cost < best_cost:
                    best_cost = cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
            return
        if not valid_plaka_set(current_plaka_set, allowed_rules):
            return
        customer_idx = coalition_list[idx]
        p = customers[customer_idx]
        options = ['s6'] if p.get("is_full_truck", False) else secim
        for option in options:
            new_plaka = p[option]["plaka"]
            new_plaka_set = current_plaka_set | {new_plaka}
            if not valid_plaka_set(new_plaka_set, allowed_rules):
                continue
            plaka_fiyat = p[option][K_type]
            new_max_K = max(current_max_K, plaka_fiyat)
            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]

    backtrack(0, {}, 0, 0, set())
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K

# ---------- HIZLI LOWER & UPPER BOUND ----------
def quick_lower_bound(customers, secim, remaining_mask):
    cost = 0
    for i in range(len(customers)):
        if remaining_mask & (1 << i):
            c = customers[i]
            min_cost = math.inf
            for opt in secim:
                if opt in c:
                    min_cost = min(min_cost, c[opt]["tır"], c[opt]["kamyon"])
            cost += min_cost
    return cost

def greedy_upper_bound(customers, secim, allowed_rules,
                       KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    order = sorted(range(len(customers)), key=lambda i: -customers[i]["ldm"])
    cost = 0
    current_ldm = 0
    for idx in order:
        ldm = customers[idx]["ldm"]
        if current_ldm + ldm <= KapasiteTır:
            current_ldm += ldm
            c = customers[idx]
            min_cost = min(c[opt]["tır"] for opt in secim if opt in c)
            cost += min_cost
        else:
            min_cost = min(c[opt]["tır"] for opt in secim if opt in c)
            cost += (min_cost + UgramaTır)
            current_ldm = ldm
    return cost

# ---------- GÜNCELLENMİŞ process_group ----------
def process_group(tarih, group_df, secim, print_to_terminal, truck_prices, allowed_rules,
                  KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon):
    sonuclar_grup = []
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor... (PID: {os.getpid()})")
    group_start_time = time.time()

    # customers listesi (ORİJİNAL KOD İLE AYNI)
    customers = []
    for _, row in group_df.iterrows():
        ldm_value = row["ldm"]
        customer = {
            "alıcı": row["alıcı"], "ldm": ldm_value, "hacim": row["hacim"],
            "il": row["data_il"], "is_full_truck": False
        }
        for opt in ['s1', 's2', 's3', 's4', 's5', 's6']:
            if opt in secim and f"plaka {opt}" in row:
                plaka = str(int(row[f"plaka {opt}"])).zfill(3)
                customer[opt] = {
                    "plaka": plaka,
                    "kamyon": truck_prices[plaka]['kamyon'],
                    "tır": truck_prices[plaka]['tır'],
                    "tldesi": row[f"tldesi {opt}"]
                }
        customers.append(customer)

    N = len(customers)

    # UB başlat
    ub = greedy_upper_bound(customers, secim, allowed_rules,
                            KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon)

    # DP ve Branch-and-Bound
    dp = [math.inf] * (1 << N)
    partition_arr = [None] * (1 << N)
    dp[0] = 0

    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        # Pruning
        lb = quick_lower_bound(customers, secim, ((1 << N) - 1) ^ mask)
        if dp[mask] + lb >= ub:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if valid_ldm([i for i in range(N) if sub & (1 << i)], customers, KapasiteTır):
                cost, assignment, K_used = compute_coalition_min_cost(
                    [i for i in range(N) if sub & (1 << i)],
                    customers, secim, allowed_rules,
                    KapasiteTır, KapasiteKamyon, UgramaTır, UgramaKamyon)
                if cost is not None:
                    new_mask = mask | sub
                    new_cost = dp[mask] + cost
                    if new_cost < dp[new_mask]:
                        dp[new_mask] = new_cost
                        partition_arr[new_mask] = (mask, sub)
                        ub = min(ub, new_cost + quick_lower_bound(customers, secim,
                                                                  (1 << N) - 1 - new_mask))
            sub = (sub - 1) & remaining

    def reconstruct(mask, partition_arr):
        if mask == 0:
            return []
        prev_mask, sub = partition_arr[mask]
        return reconstruct(prev_mask, partition_arr) + [sub]

    final_partition = reconstruct((1 << N) - 1, partition_arr) if dp[(1 << N) - 1] != math.inf else []

    # Sonuç formatı (ORİJİNAL KOD İLE AYNI)
    for sub in final_partition:
        coalition = [i for i in range(N) if sub & (1 << i)]
        cost, assignment, K_used = compute_coalition_min_cost(coalition, customers, secim,
                                                              allowed_rules, KapasiteTır,
                                                              KapasiteKamyon, UgramaTır, UgramaKamyon)
        coalition_names = [customers[i]["alıcı"] for i in coalition]
        coalition_country = [customers[i]["il"] for i in coalition]
        coalition_ldm = [int(customers[i]["ldm"]) for i in coalition]
        coalition_assignment = [assignment[i] for i in assignment if i in coalition]

        assignment_details = {
            customers[i]["alıcı"]: {'seçim': assignment[i], 'hacim': int(customers[i]["hacim"])}
            for i in assignment if i in coalition
        }
        toplam_hacim = int(sum(customers[i]["hacim"] for i in coalition))

        if print_to_terminal:
            print(f"Grup: {coalition_names}, Atamalar: {assignment_details}, Araç: {K_used}, "
                  f"Toplam Hacim: {toplam_hacim}, Maliyet: {cost}")

        sonuclar_grup.append({
            'tarih': tarih,
            'grup': str(coalition_names),
            'secim': str(coalition_assignment),
            'il': str(coalition_country),
            'hacim': str(coalition_ldm),
            'atamalar': str(assignment_details),
            'arac': K_used,
            'toplam_hacim': toplam_hacim,
            'maliyet': cost
        })

    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Grup süresi {group_duration:.2f} sn (PID: {os.getpid()})")
    return sonuclar_grup

# ---------- main blok (ORİJİNAL KOD İLE AYNI) ----------
if __name__ == '__main__':
    print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'
    secim_input = input("Kullanılacak seçenekleri virgülle girin (s1,s2,s3,s4,s5,s6): ").strip().lower()
    secim = [s.strip() for s in secim_input.split(',')] if secim_input else ['s1','s2','s3','s4','s5','s6']
    print(f"Seçilen seçenekler: {secim}")

    def clear_output_sheet(excel_path):
        try:
            with pd.ExcelFile(excel_path) as xls:
                if 'output' in xls.sheet_names:
                    user = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                    if user == 'E':
                        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                        with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                            empty_df.to_excel(writer, sheet_name='output', index=False)
                        print("'output' sekmesi temizlendi.")
                        return empty_df
                    else:
                        return pd.read_excel(excel_path, sheet_name="output")
        except Exception as e:
            empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
            return empty_df

    output_df = clear_output_sheet(excel_path)
    total_start_time = time.time()

    customers_df = pd.read_excel(excel_path, sheet_name="customers")
    customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
    grouped_customers = [(tarih, group) for tarih, group in customers_df.groupby('tarih')]

    truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
    truck_prices = {}
    for _, row in truck_prices_df.iterrows():
        plaka = str(int(row['plaka'])).zfill(3)
        truck_prices[plaka] = {'kamyon': row['kamyon'], 'tır': row['tır']}

    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()

    process_group_partial = partial(
        process_group,
        secim=secim,
        print_to_terminal=print_to_terminal,
        truck_prices=truck_prices,
        allowed_rules=allowed_rules,
        KapasiteTır=KapasiteTır,
        KapasiteKamyon=KapasiteKamyon,
        UgramaTır=UgramaTır,
        UgramaKamyon=UgramaKamyon
    )

    with multiprocessing.Pool() as pool:
        results = pool.starmap(process_group_partial, grouped_customers)

    sonuclar = []
    for res in results:
        sonuclar.extend(res)

    total_duration = time.time() - total_start_time
    if print_to_terminal:
        print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} sn")

    yeni_sonuclar_df = pd.DataFrame(sonuclar)
    output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True) if not output_df.empty else yeni_sonuclar_df

    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        output_df.to_excel(writer, sheet_name='output', index=False)

    print("\nSonuçlar başarıyla Excel'e kaydedildi.")