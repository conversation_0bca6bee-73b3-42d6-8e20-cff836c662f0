from itertools import combinations
import math
import pandas as pd

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\örnek1.xlsx"

KapasiteTır =  20275
KapasiteKamyon = 11059

UgramaTır = 1500
UgramaKamyon = 1000

print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'

def clear_output_sheet():
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        print(f"Output sekmesi oluşturuluyor: {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def valid_ldm(coalition):
    return sum(customers[i]["ldm"] for i in coalition) <= KapasiteTır

def valid_plaka_set(plaka_set):
    if len(plaka_set) > 3:
        return False
    if not plaka_set:
        return True
    for rule in allowed_rules:
        if plaka_set.issubset(rule):
            return True
    return False

def coalition_digit_count(assignment, coalition):
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in ['x', 'y', 'w'] }  # W EKLENDİ
    z_count = sum(1 for i in coalition if assignment[i] == 'z')
    return len(xyw_set) + z_count

def valid_coalition_digits(assignment, coalition):
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in ['x','y','w'] }  # W EKLENDİ
    z_set = { customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i]=='z' }
    total_count = coalition_digit_count(assignment, coalition)
    if total_count > 3:
        return False
    union_set = xyw_set.union(z_set)
    for rule in allowed_rules:
        if union_set.issubset(rule):
            return True
    return False

def coalition_size(assignment, coalition):
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition if assignment[i] in ['x','y','w'] }  # W EKLENDİ
    z_count = sum(1 for i in coalition if assignment[i]=='z')
    return len(xyw_set) + z_count

def compute_coalition_min_cost(coalition):
    n = len(coalition)
    best_cost = math.inf
    best_assignment = None
    chosen_K = None
    coalition_list = list(coalition)
    total_ldm = sum(customers[i]["ldm"] for i in coalition)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    
    def backtrack(idx, current_assignment, current_max_K, current_hacim_loss, current_plaka_set):
        nonlocal best_cost, best_assignment, chosen_K
        
        current_size = coalition_size(current_assignment, coalition_list[:idx])
        sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTır
        current_cost = current_max_K + current_hacim_loss + ((current_size - 1) * sabit_maliyet)
        if current_cost >= best_cost:
            return
        
        if idx == n:
            if valid_coalition_digits(current_assignment, coalition_list):
                c_size = coalition_size(current_assignment, coalition_list)
                cost = current_max_K + current_hacim_loss + ((c_size - 1) * sabit_maliyet)
                if cost < best_cost:
                    best_cost = cost
                    best_assignment = current_assignment.copy()
                    chosen_K = K_type
            return
        
        if not valid_plaka_set(current_plaka_set):
            return
        
        customer_idx = coalition_list[idx]
        p = customers[customer_idx]
        
        options = ['z'] if p.get("is_full_truck", False) else ['x', 'y', 'z', 'w']  # W EKLENDİ
        
        for option in options:
            new_plaka = p[option]["plaka"]
            new_plaka_set = current_plaka_set | {new_plaka}
            if not valid_plaka_set(new_plaka_set):
                continue
            
            plaka_fiyat = p[option][K_type]
            new_max_K = max(current_max_K, plaka_fiyat)
            new_hacim_loss = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            current_assignment[customer_idx] = option
            backtrack(idx + 1, current_assignment, new_max_K, new_hacim_loss, new_plaka_set)
            del current_assignment[customer_idx]
    
    backtrack(0, {}, 0, 0, set())
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment, chosen_K


def reconstruct(mask):
    if mask == 0:
        return []
    prev_mask, sub = partition[mask]
    return reconstruct(prev_mask) + [sub]

output_df = clear_output_sheet()

truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
truck_prices = {}
for _, row in truck_prices_df.iterrows():
    plaka = str(int(row['plaka'])).zfill(3)
    truck_prices[plaka] = {
        'kamyon': row['kamyon'],
        'tır': row['tır']
    }

customers_df = pd.read_excel(excel_path, sheet_name="customers")
customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
grouped_customers = customers_df.groupby('tarih')

sonuclar = []
for tarih, group_df in grouped_customers:
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor...")

    customers = []
    for _, row in group_df.iterrows():
        ldm_value = row["ldm"]
        plaka_x = str(int(row["plaka x"])).zfill(3)
        plaka_y = str(int(row["plaka y"])).zfill(3)
        plaka_z = str(int(row["plaka z"])).zfill(3)
        plaka_w = str(int(row["plaka w"])).zfill(3)  # W EKLENDİ
        
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "x": {
                "plaka": plaka_x,
                "kamyon": truck_prices[plaka_x]['kamyon'],
                "tır": truck_prices[plaka_x]['tır'],
                "tldesi": row["tldesi x"]
            },
            "y": {
                "plaka": plaka_y,
                "kamyon": truck_prices[plaka_y]['kamyon'],
                "tır": truck_prices[plaka_y]['tır'],
                "tldesi": row["tldesi y"]
            },
            "z": {
                "plaka": plaka_z,
                "kamyon": truck_prices[plaka_z]['kamyon'],
                "tır": truck_prices[plaka_z]['tır'],
                "tldesi": row["tldesi z"]
            },
            "w": {  # W EKLENDİ
                "plaka": plaka_w,
                "kamyon": truck_prices[plaka_w]['kamyon'],
                "tır": truck_prices[plaka_w]['tır'],
                "tldesi": row["tldesi w"]
            },
            "is_full_truck": False
        }
        customers.append(customer)


    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",")}).tolist()

    N = len(customers)
    def get_valid_masks(N):
        return [mask for mask in range(1, 1 << N) if valid_ldm([i for i in range(N) if mask & (1 << i)])]

    valid_masks = get_valid_masks(N)
    valid_coalitions = {}

    for mask in valid_masks:
        coalition = [i for i in range(N) if mask & (1 << i)]
        cost, assignment, K_used = compute_coalition_min_cost(coalition)
        if cost is not None:
            valid_coalitions[mask] = (cost, assignment, K_used)

    dp = [math.inf] * (1 << N)
    partition = [None] * (1 << N)
    dp[0] = 0

    for mask in range(1 << N):
        if dp[mask] == math.inf:
            continue
        remaining = ((1 << N) - 1) ^ mask
        sub = remaining
        while sub:
            if sub in valid_coalitions:
                new_mask = mask | sub
                new_cost = dp[mask] + valid_coalitions[sub][0]
                if new_cost < dp[new_mask]:
                    dp[new_mask] = new_cost
                    partition[new_mask] = (mask, sub)
            sub = (sub - 1) & remaining

    final_partition = reconstruct((1 << N) - 1)

    for sub in final_partition:
        cost, assignment, K_used = valid_coalitions[sub]
        coalition_names = [customers[i]["alıcı"] for i in range(N) if sub & (1 << i)]
        coalition_country = [customers[i]["il"] for i in range(N) if sub & (1 << i)]
        coalition_ldm = [int(customers[i]["ldm"]) for i in range(N) if sub & (1 << i)]
        coalition_assignment = [assignment[i] for i in range(N) if sub & (1 << i)]

        assignment_details = {
            customers[i]["alıcı"]: {
                'seçim': assignment[i],
                'hacim': int(customers[i]["hacim"])
            } for i in assignment if sub & (1 << i)
        }
        
        toplam_hacim = int(sum(customers[i]["hacim"] for i in range(N) if sub & (1 << i)))
        
        if print_to_terminal:
            print(f"Grup: {coalition_names}, Grup Atamaları: {assignment_details}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim}, Grup Maliyeti: {cost}")
        
        sonuclar.append({
            'tarih': tarih,
            'grup': str(coalition_names),
            'secim': str(coalition_assignment),
            'il': str(coalition_country),
            'hacim': str(coalition_ldm),
            'atamalar': str(assignment_details),
            'arac': K_used,
            'toplam_hacim': toplam_hacim,
            'maliyet': cost
        })

yeni_sonuclar_df = pd.DataFrame(sonuclar)
if not output_df.empty:
    output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
else:
    output_df = yeni_sonuclar_df

with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    output_df.to_excel(writer, sheet_name='output', index=False)

print("\nSonuçlar başarıyla Excel'e kaydedildi.")
