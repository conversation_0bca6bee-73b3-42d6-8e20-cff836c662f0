# from itertools import combinations # OR-Tools için artık doğrudan bu gerekmeyebilir
import math
import pandas as pd
import time
from ortools.sat.python import cp_model # OR-Tools CP-SAT için eklendi

excel_path = r"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\_slms_\\Rota_oluşturma\\DATA_PEYMAN1.xlsx"

KapasiteTir = 20275
KapasiteKamyon = 11059

UgramaTir = 1500
UgramaKamyon = 1000

print_to_terminal = input("Sonuçları ekrana yazdırmak ister misiniz? (E/H): ").strip().upper() == 'E'
# Tüm işlemlerin toplam süresi için
total_start_time = time.time()

def clear_output_sheet():
    try:
        with pd.ExcelFile(excel_path) as xls:
            if 'output' in xls.sheet_names:
                user_input = input("Mevcut 'output' sekmesindeki verileri temizlemek ister misiniz? (E/H): ").strip().upper()
                if user_input == 'E':
                    empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
                    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                        empty_df.to_excel(writer, sheet_name='output', index=False)
                    print("'output' sekmesi temizlendi.")
                    return empty_df
                else:
                    print("Mevcut veriler korunacak.")
                    return pd.read_excel(excel_path, sheet_name="output")
    except Exception as e:
        # Eğer dosya yoksa veya output sekmesi yoksa yeni bir DataFrame oluştur
        print(f"Output sekmesi oluşturuluyor veya okunurken hata (yeni df oluşturulacak): {e}")
        empty_df = pd.DataFrame(columns=['tarih', 'grup', 'atamalar', 'arac', 'toplam_hacim', 'maliyet', 'ülke', 'ldm'])
        return empty_df

def valid_ldm(coalition_indices): # Parametre adı güncellendi
    # customers global değişkenine erişiyor
    return sum(customers[i]["ldm"] for i in coalition_indices) <= KapasiteTir

def valid_plaka_set(plaka_set):
    if len(plaka_set) > 3:
        return False
    if not plaka_set:
        return True
    # allowed_rules global değişkenine erişiyor
    for rule in allowed_rules:
        if plaka_set.issubset(rule):
            return True
    return False

def coalition_digit_count(assignment, coalition_indices): # Parametre adı güncellendi
    # customers global değişkenine erişiyor
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition_indices if assignment[i] in ['x', 'y', 'w'] }
    z_count = sum(1 for i in coalition_indices if assignment[i] == 'z')
    return len(xyw_set) + z_count

def valid_coalition_digits(assignment, coalition_indices): # Parametre adı güncellendi
    # customers global değişkenine erişiyor
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition_indices if assignment[i] in ['x','y','w'] }
    z_set = { customers[i][assignment[i]]["plaka"] for i in coalition_indices if assignment[i]=='z' }
    total_count = coalition_digit_count(assignment, coalition_indices) # coalition_digit_count çağrısı düzeltildi
    if total_count > 3:
        return False
    union_set = xyw_set.union(z_set)
    # allowed_rules global değişkenine erişiyor
    for rule in allowed_rules:
        if union_set.issubset(rule):
            return True
    return False

def coalition_size(assignment, coalition_indices): # Parametre adı güncellendi
    # customers global değişkenine erişiyor
    xyw_set = { customers[i][assignment[i]]["plaka"] for i in coalition_indices if assignment[i] in ['x','y','w'] }
    z_count = sum(1 for i in coalition_indices if assignment[i]=='z')
    return len(xyw_set) + z_count

def compute_coalition_min_cost(coalition_indices): # Parametre adı coalition_indices olarak güncellendi
    n = len(coalition_indices)
    best_cost = math.inf
    best_assignment_dict = None # best_assignment dict olarak değiştirildi
    chosen_K = None
    # coalition_list = list(coalition) # Artık coalition_indices direkt liste
    total_ldm = sum(customers[i]["ldm"] for i in coalition_indices)
    K_type = "kamyon" if total_ldm <= KapasiteKamyon else "tır"
    
    # customers global değişkenine erişiyor
    
    def backtrack(idx, current_assignment_dict, current_max_K, current_hacim_loss, current_plaka_set_arg):
        nonlocal best_cost, best_assignment_dict, chosen_K # best_assignment_dict olarak güncellendi
        
        # coalition_indices'in ilk idx elemanı üzerinden boyut hesaplaması
        # Bu kısım current_assignment_dict'in anahtarlarını kullanmalı
        # coalition_list[:idx] yerine current_assignment_dict.keys() kullanılabilir
        # ancak bu backtrack'in mantığına göre idx ile customer_idx eşleşmeli
        
        # Geçerli ataması yapılmış müşteri indeksleri
        assigned_customer_indices_so_far = list(current_assignment_dict.keys())

        current_size = 0
        if assigned_customer_indices_so_far: # Eğer en az bir atama yapıldıysa
             current_size = coalition_size(current_assignment_dict, assigned_customer_indices_so_far)


        sabit_maliyet = UgramaKamyon if K_type == "kamyon" else UgramaTir
        # current_size 0 ise (yani hiç plaka yoksa) (current_size - 1) negatif olabilir, 
        # bu yüzden max(0, current_size - 1) kullanılabilir veya mantık gözden geçirilmeli.
        # Orijinal kodda (c_size - 1) idi, c_size en az 1 olmalı eğer bir rota varsa.
        # Eğer current_size 0 ise, maliyet sadece current_max_K + current_hacim_loss olmalı.
        # Eğer current_size 1 ise, uğrama maliyeti olmamalı.
        ugrama_maliyeti_hesapla = 0
        if current_size > 0: # Tek bir durak varsa uğrama maliyeti olmaz (kendi kendine uğrama)
            ugrama_maliyeti_hesapla = (current_size -1) * sabit_maliyet if current_size > 1 else 0


        current_cost = current_max_K + current_hacim_loss + ugrama_maliyeti_hesapla

        if current_cost >= best_cost:
            return
        
        if idx == n: # Tüm müşteriler için bir seçenek atandıysa
            # coalition_indices listesindeki tüm müşterilerin atamasının yapıldığından emin ol
            if valid_coalition_digits(current_assignment_dict, coalition_indices):
                # c_size'ı tüm koalisyon için yeniden hesapla
                c_size_final = coalition_size(current_assignment_dict, coalition_indices)
                ugrama_maliyeti_final = (c_size_final - 1) * sabit_maliyet if c_size_final > 1 else 0
                cost = current_max_K + current_hacim_loss + ugrama_maliyeti_final
                if cost < best_cost:
                    best_cost = cost
                    best_assignment_dict = current_assignment_dict.copy()
                    chosen_K = K_type
            return
        
        # Bu plaka seti kontrolü, mevcut durumda oluşturulan plaka setinin kurallara uyup uymadığını kontrol eder.
        #idx ilerledikçe current_plaka_set_arg genişler.
        # if not valid_plaka_set(current_plaka_set_arg): # Bu kontrol burada erken olabilir, tüm seçenekler denenmeli
        #     return

        customer_idx = coalition_indices[idx] # coalition_indices'den sıradaki müşteri indeksi
        p = customers[customer_idx]
        
        options = ['z'] if p.get("is_full_truck", False) else ['x', 'y', 'z', 'w']
        
        for option in options:
            new_plaka_for_option = p[option]["plaka"]
            
            # Geçici yeni plaka seti: mevcut plaka setine bu opsiyonun plakasını ekle
            # Bu adımda valid_plaka_set kontrolü yapılabilir.
            # Ancak orijinal kodda bu kontrol backtrack başında genel bir set için yapılıyordu.
            # Burada her adımda bir sonraki adımı etkileyecek şekilde kontrol daha mantıklı olabilir.
            # temp_plaka_set_for_check = current_plaka_set_arg | {new_plaka_for_option}
            # if not valid_plaka_set(temp_plaka_set_for_check):
            #    continue

            plaka_fiyat = p[option][K_type]
            new_max_K_val = max(current_max_K, plaka_fiyat)
            new_hacim_loss_val = current_hacim_loss + (p["hacim"] * p[option]["tldesi"])
            
            current_assignment_dict[customer_idx] = option # Atamayı yap
            
            # Yeni plaka setini oluştur (sadece mevcut atamalara göre)
            # Bu, bir sonraki backtrack adımına gönderilecek olan plaka setidir.
            # Orijinal kodda current_plaka_set tüm coalition için oluşturuluyordu, bu daha doğru.
            updated_plaka_set_for_next_call = { customers[ci][current_assignment_dict[ci]]["plaka"] 
                                                for ci in current_assignment_dict 
                                                if current_assignment_dict[ci] in ['x', 'y', 'w'] }
            
            # Plaka seti kontrolü, bir sonraki adıma geçmeden önce yapılabilir.
            # Bu, 'w' gibi seçeneklerin plaka sayısını artırdığı durumlar için önemlidir.
            # Ancak, orijinal kodda valid_plaka_set, mevcut plakaların bir alt küme olup olmadığını kontrol ediyor.
            # valid_coalition_digits ise tüm atamalar yapıldıktan sonra son kontrolü yapıyor.
            # Orijinal yapıya sadık kalmak için, valid_plaka_set'i belki en başta bir kez çağırmak
            # veya valid_coalition_digits'e güvenmek daha uygun olabilir.
            # Şimdilik valid_plaka_set kontrolünü valid_coalition_digits içinde bırakalım.
            # Ancak backtrack'in erken sonlanması için bir plaka sayısı sınırı (örn >3 ise direkt kes) eklenebilir.
            
            # Erken kesme için basit bir kontrol: Eğer xyw plakaları 3'ü geçerse ve allowed_rules'a uymuyorsa kes.
            # Bu, valid_plaka_set'in bir parçası.
            # if not valid_plaka_set(updated_plaka_set_for_next_call):
            #    del current_assignment_dict[customer_idx] # Atamayı geri al
            #    continue


            backtrack(idx + 1, current_assignment_dict, new_max_K_val, new_hacim_loss_val, updated_plaka_set_for_next_call)
            del current_assignment_dict[customer_idx] # Atamayı geri al (backtrack)
    
    backtrack(0, {}, 0, 0, set()) # Başlangıçta boş atama, maliyet ve plaka seti
    if best_cost == math.inf:
        return None, None, None
    return best_cost, best_assignment_dict, chosen_K


# reconstruct fonksiyonu CP-SAT sonrası farklı şekilde ele alınacak, bu yüzden kaldırılabilir veya yorum satırı yapılabilir.
# def reconstruct(mask):
#     if mask == 0:
#         return []
#     prev_mask, sub = partition[mask]
#     return reconstruct(prev_mask) + [sub]

output_df = clear_output_sheet()

truck_prices_df = pd.read_excel(excel_path, sheet_name="truckprice")
truck_prices = {}
for _, row in truck_prices_df.iterrows():
    plaka = str(int(row['plaka'])).zfill(3)
    truck_prices[plaka] = {
        'kamyon': row['kamyon'],
        'tır': row['tır']
    }

customers_df = pd.read_excel(excel_path, sheet_name="customers")
customers_df['tarih'] = pd.to_datetime(customers_df['tarih'])
grouped_customers = customers_df.groupby('tarih')

sonuclar = [] # Bu liste, tüm tarihler için genel sonuçları tutacak
# output_df = pd.DataFrame() # Her grup için ayrı df yerine tek df kullanılacaksa

for tarih, group_df in grouped_customers:
    group_start_time = time.time()
    print(f"\n{tarih.strftime('%Y-%m-%d')} tarihli planlamaya başlanıyor...")

    customers = [] # Bu global değil, her grup için yeniden oluşturuluyor. Fonksiyonlar bunu kullanacak.
    customer_map = {} # Müşteri adından index'e map, gerekirse.
    for idx, row in group_df.reset_index(drop=True).iterrows(): # index'i sıfırlayarak N'i doğru alalım
        ldm_value = row["ldm"]
        plaka_x = str(int(row["plaka x"])).zfill(3)
        plaka_y = str(int(row["plaka y"])).zfill(3)
        plaka_z = str(int(row["plaka z"])).zfill(3)
        plaka_w = str(int(row["plaka w"])).zfill(3)
        
        customer = {
            "alıcı": row["alıcı"],
            "ldm": ldm_value,
            "hacim": row["hacim"],
            "il": row["data_il"],
            "x": { "plaka": plaka_x, "kamyon": truck_prices[plaka_x]['kamyon'], "tır": truck_prices[plaka_x]['tır'], "tldesi": row["tldesi x"]},
            "y": { "plaka": plaka_y, "kamyon": truck_prices[plaka_y]['kamyon'], "tır": truck_prices[plaka_y]['tır'], "tldesi": row["tldesi y"]},
            "z": { "plaka": plaka_z, "kamyon": truck_prices[plaka_z]['kamyon'], "tır": truck_prices[plaka_z]['tır'], "tldesi": row["tldesi z"]},
            "w": { "plaka": plaka_w, "kamyon": truck_prices[plaka_w]['kamyon'], "tır": truck_prices[plaka_w]['tır'], "tldesi": row["tldesi w"]},
            "is_full_truck": False # Bu değer Excel'den gelmiyorsa varsayılan olarak False
        }
        customers.append(customer)
        customer_map[row["alıcı"]] = idx


    rules_df = pd.read_excel(excel_path, sheet_name="allowed_rules", header=None)
    allowed_rules = rules_df[0].apply(lambda x: {s.strip() for s in str(x).split(",") if s.strip()}).tolist() # Boş stringleri filtrele

    N = len(customers)
    if N == 0:
        print(f"{tarih.strftime('%Y-%m-%d')} için müşteri bulunamadı.")
        continue
        
    print(f"{N} müşteri için koalisyon maliyetleri hesaplanıyor...")
    
    # Orijinal koddaki valid_masks ve valid_coalitions oluşturma mantığı korunuyor
    # valid_masks, LDM'ye uyan tüm alt kümeleri (bitmask olarak) içerir
    # valid_coalitions ise bu alt kümeler için compute_coalition_min_cost ile hesaplanan maliyetleri içerir
    
    possible_coalition_masks = []
    for i in range(1, 1 << N): # 1'den başlayarak tüm olası alt kümeler (boş olmayan)
        # Mask'tan müşteri indeks listesi oluştur
        current_coalition_indices = [j for j in range(N) if (i >> j) & 1]
        if valid_ldm(current_coalition_indices): # LDM kontrolü
            possible_coalition_masks.append(i)

    valid_coalitions = {} # {mask: (cost, assignment_dict, K_used)}
    #print(f"Olası LDM uyumlu {len(possible_coalition_masks)} koalisyon için maliyet hesaplanacak.")
    
    processed_count = 0
    for mask in possible_coalition_masks:
        coalition_indices = [j for j in range(N) if (mask >> j) & 1]
        cost, assignment_dict, K_used = compute_coalition_min_cost(coalition_indices)
        if cost is not None:
            valid_coalitions[mask] = (cost, assignment_dict, K_used)
        processed_count += 1
        if processed_count % 100 == 0 and print_to_terminal:
             print(f"...{processed_count}/{len(possible_coalition_masks)} koalisyon işlendi...")
    
    if not valid_coalitions:
        print(f"{tarih.strftime('%Y-%m-%d')} için geçerli koalisyon bulunamadı.")
        continue
    
    print(f"Geçerli {len(valid_coalitions)} koalisyon bulundu. CP-SAT ile en iyi bölümleme aranıyor...")

    # --- CP-SAT ile Set Partitioning ---
    model = cp_model.CpModel()

    # Karar değişkenleri: Her bir geçerli koalisyon seçilir mi (True) seçilmez mi (False)
    # x[mask] koalisyonun seçilip seçilmediğini tutar.
    x = {}
    for mask in valid_coalitions:
        x[mask] = model.NewBoolVar(f'coalition_{mask}')

    # Amaç fonksiyonu: Seçilen koalisyonların toplam maliyetini minimize et
    total_cost_terms = []
    for mask in valid_coalitions:
        total_cost_terms.append(x[mask] * int(valid_coalitions[mask][0])) # Maliyet int olmalı
    model.Minimize(sum(total_cost_terms))

    # Kısıtlar: Her müşteri tam olarak bir seçilmiş koalisyonda yer almalı
    for j in range(N): # Her bir müşteri için
        customer_coverage_terms = []
        for mask in valid_coalitions:
            if (mask >> j) & 1: # Eğer j müşterisi bu mask (koalisyon) içindeyse
                customer_coverage_terms.append(x[mask])
        if customer_coverage_terms: # Eğer müşteri en az bir koalisyonda yer alabiliyorsa
            model.Add(sum(customer_coverage_terms) == 1)
        else:
            # Bu durum, bir müşterinin hiçbir geçerli koalisyona dahil edilemediği anlamına gelir.
            # Bu, compute_coalition_min_cost'un bazı tekil müşteriler için bile None döndürdüğü
            # veya LDM kısıtları vb. nedenlerle olabilir. Bu durumu ele almak gerekebilir.
            # Şimdilik, böyle bir müşteri varsa model çözümsüz olabilir.
            print(f"UYARI: {customers[j]['alıcı']} adlı müşteri hiçbir geçerli koalisyona atanamıyor. Model çözümsüz olabilir.")
            # Çözümsüzlüğü engellemek için bu müşteriyi opsiyonel yapabilir veya bir ceza maliyeti ekleyebilirsiniz.
            # Basitlik adına şimdilik bu kısıtı eklemiyoruz eğer customer_coverage_terms boşsa.
            # Ancak bu, o müşterinin açıkta kalacağı anlamına gelir.
            # Daha doğru bir yaklaşım, bu müşteriler için çok yüksek maliyetli dummy koalisyonlar eklemek olabilir.
            # VEYA model.Add(sum(customer_coverage_terms) == 0) eğer müşteri hiç kapsanmayacaksa
            # Ama set partitioning'de her müşteri kapsanmalı.
            # Eğer bir müşteri hiçbir LDM'e uyan koalisyonda değilse, hata verir.
            # Bu yüzden yukarıdaki valid_coalitions hesaplamasında bu durumlar zaten elenmiş olmalı.
            # Her müşterinin en azından tek başına bir koalisyon oluşturabildiğinden emin olunmalı.
            pass


    # Çözücüyü oluştur ve modeli çöz
    solver = cp_model.CpSolver()
    # Zaman sınırı ekleyebilirsiniz: solver.parameters.max_time_in_seconds = 60.0
    status = solver.Solve(model)

    final_chosen_coalition_masks = []
    if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
        print(f"CP-SAT Çözüm bulundu! Toplam Maliyet: {solver.ObjectiveValue()}")
        for mask in valid_coalitions:
            if solver.Value(x[mask]) == 1:
                final_chosen_coalition_masks.append(mask)
        
        # Sonuçları işle ve kaydet (orijinal koddaki gibi)
        for sub_mask in final_chosen_coalition_masks: # sub_mask artık seçilen koalisyonun maskesi
            cost, assignment_dict, K_used = valid_coalitions[sub_mask]
            
            # Müşteri indekslerini mask'tan al
            current_coalition_indices_from_mask = [i for i in range(N) if (sub_mask >> i) & 1]

            coalition_names = [customers[i]["alıcı"] for i in current_coalition_indices_from_mask]
            coalition_country = list(set(customers[i]["il"] for i in current_coalition_indices_from_mask)) # Tekil ülke listesi
            coalition_ldm_values = [int(customers[i]["ldm"]) for i in current_coalition_indices_from_mask] # ldm değerleri

            # assignment_dict zaten doğru müşteri indekslerini içermeli
            # Anahtarlar global 'customers' listesindeki indekslerdir.
            assignment_details_for_output = {
                customers[i]["alıcı"]: {
                    'seçim': assignment_dict[i], # assignment_dict[i] müşteri i'nin seçimi
                    'hacim': int(customers[i]["hacim"])
                } for i in assignment_dict # assignment_dict'in anahtarları zaten bu koalisyondaki müşteri indeksleridir
            }
            
            # coalition_assignment: Seçilen koalisyondaki müşterilerin atama (x,y,z,w) listesi
            # Bu, assignment_dict'ten doğru sıralamada (coalition_names'e göre) alınmalı
            # Ya da doğrudan assignment_details_for_output'taki seçimler kullanılabilir.
            # Orijinal kodda assignment_details'dan farklı bir coalition_assignment vardı.
            # assignment_dict'in anahtarları müşteri index'i, değeri ise x,y,z,w seçimi.
            # coalitition_names'deki sırayla bu seçimleri alalım:
            ordered_assignments_for_output = [assignment_dict[customer_map[c_name]] for c_name in coalition_names if customer_map[c_name] in assignment_dict]


            toplam_hacim_bu_koalisyon = int(sum(customers[i]["hacim"] for i in current_coalition_indices_from_mask))
            
            if print_to_terminal:
                print(f"Grup: {coalition_names}, Grup Atamaları: {assignment_details_for_output}, Kullanılan Araç: {K_used}, Toplam Hacim: {toplam_hacim_bu_koalisyon}, Grup Maliyeti: {cost}")
            
            sonuclar.append({
                'tarih': tarih.strftime('%Y-%m-%d'), # Tarihi string olarak kaydetmek daha iyi olabilir Excel için
                'grup': str(coalition_names),
                'secim': str(ordered_assignments_for_output), # coalition_assignment yerine
                'il': str(coalition_country),
                'hacim': str(coalition_ldm_values), # ldm değerleri
                'atamalar': str(assignment_details_for_output),
                'arac': K_used,
                'toplam_hacim': toplam_hacim_bu_koalisyon,
                'maliyet': cost
            })

    elif status == cp_model.INFEASIBLE:
        print("CP-SAT Çözüm bulunamadı: Model çözümsüz (INFEASIBLE).")
    else:
        print(f"CP-SAT Çözüm optimal değil veya bulunamadı. Durum: {status}")

    group_duration = time.time() - group_start_time
    if print_to_terminal:
        print(f"Bu grup işlemi {group_duration:.2f} saniye sürdü (CP-SAT dahil)")


# ---- Tüm gruplar işlendikten sonra Excel'e yazma ----
if sonuclar: # Eğer en az bir sonuç varsa
    yeni_sonuclar_df = pd.DataFrame(sonuclar)
    
    # output_df'i en başta clear_output_sheet'ten aldık.
    # Eğer boşsa veya korunmuşsa ona göre birleştirme.
    if not output_df.empty:
        final_output_df = pd.concat([output_df, yeni_sonuclar_df], ignore_index=True)
    else: # output_df baştan boş oluşturulduysa
        final_output_df = yeni_sonuclar_df
    
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            final_output_df.to_excel(writer, sheet_name='output', index=False)
        print("\nSonuçlar başarıyla Excel'e kaydedildi.")
    except Exception as e:
        print(f"\nExcel'e yazma sırasında hata: {e}")
        print("Sonuçlar DataFrame olarak mevcuttur, elle kaydedebilirsiniz.")
        # print(final_output_df)

else:
    print("\nKaydedilecek sonuç bulunamadı.")

total_duration = time.time() - total_start_time
if print_to_terminal:
    print(f"\nTOPLAM İŞLEM SÜRESİ: {total_duration:.2f} saniye")